<script setup lang="ts">
import { promotion_list } from '@/api/mine/index'
import router from '@/router'
// const show = ref(true)
const show = defineModel<boolean>()
const rightsList = ref([])
function gotoMember() {
  router.push({ name: 'Member' })
  show.value = false
}

watch(
  () => show.value,
  (val) => {
    if (val) {
      promotion_list({ type: '高级' }).then((res) => {
        if (res.code !== 200) return
        rightsList.value = res.data.map((item) => {
          return item.interest_list.map((citem) => {
            return {
              ...citem
            }
          })
        })
        rightsList.value = rightsList.value.reduce((acc, val) => acc.concat(val), []).slice(0, 4)
      })
    }
  }
)
</script>
<template>
  <van-dialog
    v-model:show="show"
    show-cancel-button
    class="loginDialog flex-center-center"
  >
    <!--    <div-->
    <!--      class="item mb-12 flex-start-center"-->
    <!--      v-for="item in rightsList"-->
    <!--      :key="item.id"-->
    <!--    >-->
    <!--      <van-image-->
    <!--        :src="item.icon"-->
    <!--        class="rights-icon"-->
    <!--      />-->
    <!--      <div class="rights-text">{{ item.name }}</div>-->
    <!--    </div>-->
    <div class="member-box">1</div>
  </van-dialog>
</template>
<style lang="scss"></style>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

.loginDialog {
  background-color: transparent;
  width: 100%;
  height: 100%;
}
</style>
